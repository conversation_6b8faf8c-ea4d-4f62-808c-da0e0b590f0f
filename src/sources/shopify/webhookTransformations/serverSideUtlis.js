/* eslint-disable no-param-reassign */
const get = require('get-value');
const { isDefinedAndNotNull, uuidv5 } = require('@rudderstack/integrations-lib');
const { extractEmailFromPayload } = require('../tracker/util');
const { constructPayload } = require('../../../v0/util');
const { INTEGERATION, lineItemsMappingJSON, productMappingJSON } = require('../config');
const { RedisDB } = require('../../../util/redis/redisConnector');
const stats = require('../../../util/stats');

/**
 * Returns an array of products from the lineItems array received from the webhook event
 * @param {Array} lineItems
 * @param {Object} mapping
 * @returns {Array} products
 */
const getProductsFromLineItems = (lineItems, mapping) => {
  if (!lineItems || lineItems.length === 0) {
    return [];
  }
  const products = [];
  lineItems.forEach((lineItem) => {
    const product = constructPayload(lineItem, mapping);
    products.push(product);
  });
  return products;
};

/**
 * Creates properties for the ecommerce webhook events received from the pixel based app
 * @param {Object} message
 * @returns {Object} properties
 */
const createPropertiesForEcomEventFromWebhook = (message, shopifyTopic) => {
  const { line_items: lineItems } = message;
  if (!lineItems || lineItems.length === 0) {
    return [];
  }
  const mappedPayload = constructPayload(message, productMappingJSON);
  if (shopifyTopic === 'orders_updated' || shopifyTopic === 'checkouts_update') {
    delete mappedPayload.value;
  }
  mappedPayload.products = getProductsFromLineItems(lineItems, lineItemsMappingJSON);
  return mappedPayload;
};

/**
 * Returns the anonymousId from the noteAttributes array in the webhook event
 * @param {Object} event
 * @returns {String} anonymousId
 */
const getAnonymousIdFromAttributes = (event) => {
  if (!isDefinedAndNotNull(event) || !isDefinedAndNotNull(event.note_attributes)) {
    return null; // Return early if event or note_attributes is invalid
  }

  const noteAttributes = event.note_attributes;
  const rudderAnonymousIdObject = noteAttributes.find((attr) => attr.name === 'rudderAnonymousId');

  return rudderAnonymousIdObject ? rudderAnonymousIdObject.value : null;
};

/**
 * Returns the cart_token from the event message
 * @param {Object} event
 * @returns {String} cart_token
 */
const getCartToken = (event) => event?.cart_token || null;

/**
 * Adds the cartTokenHash to the traits object in the message
 * @param {Object} message
 * @param {String} event
 * */
const addCartTokenHashToTraits = (message, event) => {
  const cartToken = getCartToken(event);
  if (cartToken) {
    const cartTokenHash = uuidv5(cartToken);
    message.context.traits = {
      ...message.context.traits,
      cart_token_hash: cartTokenHash,
    };
  }
  return message;
};

/**
 * Handles the anonymousId assignment for the message, based on the event attributes and redis data
 * @param {Object} message rudderstack message object
 * @param {Object} event raw shopify event payload
 * @param {Object} metricMetadata metric metadata object
 */
const setAnonymousId = async (message, event, metricMetadata) => {
  const anonymousId = getAnonymousIdFromAttributes(event);
  const cartToken = getCartToken(event);
  const cartTokenHash = cartToken ? uuidv5(cartToken) : null;
  if (isDefinedAndNotNull(anonymousId)) {
    message.anonymousId = anonymousId;
  }
  // if anonymousId is not present in note_attributes or note_attributes is not present, query redis for anonymousId
  // when cart_token is present
  else if (cartToken) {
    const redisData = await RedisDB.getVal(`pixel:${cartToken}`);
    if (redisData?.anonymousId) {
      message.anonymousId = redisData.anonymousId;
      stats.increment('shopify_pixel_cart_token_mapping', {
        action: 'stitchCartTokenToAnonId',
        operation: 'get',
      });
    } else {
      // if anonymousId is not present in note_attributes or redis, generate a new anonymousId
      // the anonymousId will be generated by hashing the cart_token using uuidv5
      // this hash will be present in the traits object as cart_token_hash
      message.anonymousId = cartTokenHash;
      stats.increment('shopify_pixel_id_stitch_gaps', {
        event: message.event,
        reason: 'redis_cache_miss',
        source: metricMetadata.source,
        writeKey: metricMetadata.writeKey,
      });
    }
  } else {
    stats.increment('shopify_pixel_id_stitch_gaps', {
      event: message.event,
      reason: 'cart_token_miss',
      source: metricMetadata.source,
      writeKey: metricMetadata.writeKey,
    });
  }
};

/**
  Handles email and contextual properties enrichment for the message payload
 * @param {Object} message rudderstack message object
 * @param {Object} event raw shopify event payload
 * @param {String} shopifyTopic shopify event topic
*/
const handleCommonProperties = (message, event, shopifyTopic) => {
  if (!get(message, 'context.traits.email')) {
    const email = extractEmailFromPayload(event);
    if (email) {
      message.setProperty('context.traits.email', email);
    }
  }
  message.setProperty(`integrations.${INTEGERATION}`, true);
  message.setProperty('context.library', {
    eventOrigin: 'server',
    name: 'RudderStack Shopify Cloud',
    version: '2.0.0',
  });
  message.setProperty('context.topic', shopifyTopic);
  // attaching cart, checkout and order tokens in context object
  message.setProperty(`context.cart_token`, event.cart_token);
  message.setProperty(`context.checkout_token`, event.checkout_token);
  // raw shopify payload passed inside context object under shopifyDetails
  message.setProperty('context.shopifyDetails', event);
  if (shopifyTopic === 'orders_updated') {
    message.setProperty(`context.order_token`, event.token);
  }
  message.setProperty('integrations.DATA_WAREHOUSE', {
    options: {
      jsonPaths: [`${message.type}.context.shopifyDetails`],
    },
  });
  return message;
};

module.exports = {
  createPropertiesForEcomEventFromWebhook,
  getCartToken,
  getProductsFromLineItems,
  getAnonymousIdFromAttributes,
  setAnonymousId,
  handleCommonProperties,
  addCartTokenHashToTraits,
};
