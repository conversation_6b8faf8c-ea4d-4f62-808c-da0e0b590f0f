name: Publish New GitHub Release

on:
  pull_request:
    types:
      - closed
    branches:
      - main

jobs:
  release:
    name: Publish New GitHub Release
    runs-on: ubuntu-latest

    if: (startsWith(github.event.pull_request.head.ref, 'release/') || startsWith(github.event.pull_request.head.ref, 'hotfix-release/')) && github.event.pull_request.merged == true # only merged pull requests must trigger this job

    steps:
      - name: Extract Version
        id: extract-version
        run: |
          branch_name="${{ github.event.pull_request.head.ref }}"
          version=${branch_name#hotfix-}
          version=${version#release/v}

          echo "release_version=$version" >> $GITHUB_OUTPUT

      - name: Checkout
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4.4.0
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Install Dependencies
        env:
          HUSKY: 0
        run: |
          npm ci

      # In order to make a commit, we need to initialize a user.
      # You may choose to write something less generic here if you want, it doesn't matter functionality wise.
      - name: Initialize Mandatory Git Config
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Tag & Create GitHub Release
        id: create_release
        continue-on-error: true
        env:
          HUSKY: 0
          GITHUB_TOKEN: ${{ secrets.PAT }}
          CONVENTIONAL_GITHUB_RELEASER_TOKEN: ${{ secrets.PAT }}
        run: |
          git fetch --tags origin
          git tag -a v${{ steps.extract-version.outputs.release_version }} -m "chore: release v${{ steps.extract-version.outputs.release_version }}"
          git push origin refs/tags/v${{ steps.extract-version.outputs.release_version }}
          npm run release:github
          echo "DATE=$(date)" >> $GITHUB_ENV

      - name: Pull Changes Into develop Branch
        run: |
          gh pr create \
            --base develop \
            --head main \
            --title "chore(release): pull main into develop post release v${{ steps.extract-version.outputs.release_version }}" \
            --body ":crown: *An automated PR*"
        env:
          GH_TOKEN: ${{ secrets.PAT }}

      - name: Delete Release Branch
        uses: koj-co/delete-merged-action@master
        if: startsWith(github.event.pull_request.head.ref, 'release/')
        with:
          branches: 'release/*'
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}

      - name: Delete Hotfix Release Branch
        uses: koj-co/delete-merged-action@master
        if: startsWith(github.event.pull_request.head.ref, 'hotfix-release/')
        with:
          branches: 'hotfix-release/*'
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}

      - name: Notify Slack Channel
        id: slack
        uses: slackapi/slack-github-action@v1.27.0
        continue-on-error: true
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          PROJECT_NAME: 'Rudder Transformer'
          RELEASES_URL: 'https://github.com/rudderlabs/rudder-transformer/releases/tag/'
        with:
          channel-id: ${{ secrets.SLACK_RELEASE_CHANNEL_ID }}
          payload: |
            {
              "text": "*<${{env.RELEASES_URL}}v${{ steps.extract-version.outputs.release_version }}|v${{ steps.extract-version.outputs.release_version }}>*\nCC: <!subteam^S02AEQL26CT> <!subteam^S03SEKBFX0D> <@U021E50QAGY>",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":tada:  ${{ env.PROJECT_NAME }} - New GitHub Release  :tada:"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{env.RELEASES_URL}}v${{ steps.extract-version.outputs.release_version }}|v${{ steps.extract-version.outputs.release_version }}>*\nCC: <!subteam^S02AEQL26CT> <!subteam^S03SEKBFX0D> <@U021E50QAGY>"
                  }
                }
              ]
            }
