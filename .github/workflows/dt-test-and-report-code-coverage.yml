name: DT Tests
on:
  workflow_call:
    secrets:
      SONAR_TOKEN:
        required: true

  pull_request:
    types: ['opened', 'reopened', 'synchronize']

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.sha }}
  cancel-in-progress: true

jobs:
  get_workflow_url:
    runs-on: ubuntu-latest
    steps:
      - id: get_url
        run: |
          curl -s https://api.github.com/repos/${{ github.repository }}/actions/workflows/${{ github.workflow }}/runs/${{ github.run_id }} | jq -r .html_url >> workflow_url.txt
          echo "::set-output name=workflow_url::$(cat workflow_url.txt)"
    outputs:
      url: ${{ steps.get_url.outputs.workflow_url }}

  coverage:
    name: Code Coverage
    runs-on: ubuntu-latest
    needs: [get_workflow_url]
    outputs:
      tests_run_outcome: ${{steps.run_tests.outcome}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4.4.0
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Install Dependencies
        run: npm ci

      - name: Run Tests
        id: run_tests
        continue-on-error: true
        run: |
          # Supress logging in tests
          LOG_LEVEL=100 npm run test:js:ci
          LOG_LEVEL=100 npm run test:ts:ci

      - name: Run Lint Checks
        run: |
          npm run check:lint
          npm run lint:fix

      - name: Upload Coverage Reports to Codecov
        uses: codecov/codecov-action@v5.4.2
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
        with:
          directory: ./reports/coverage

      - name: Upload TS Coverage Reports to Codecov
        uses: codecov/codecov-action@v5.4.2
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
        with:
          directory: ./reports/ts-coverage

      - name: Fix filesystem paths in generated reports
        if: always()
        run: |
          sed -i 's+home/runner/work/rudder-transformer/rudder-transformer+/github/workspace+g' reports/coverage/lcov.info
          sed -i 's+home/runner/work/rudder-transformer/rudder-transformer+/github/workspace+g' reports/ts-coverage/lcov.info
          sed -i 's+/home/<USER>/work/rudder-transformer/rudder-transformer+/github/workspace+g' reports/eslint.json

      - name: SonarCloud Scan
        if: always()
        uses: SonarSource/sonarqube-scan-action@v5.2.0
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  notify:
    name: slack notification on failure
    needs: [get_workflow_url, coverage]
    if: needs.coverage.outputs.tests_run_outcome == 'failure' || failure()
    uses: ./.github/workflows/slack-notify.yml
    with:
      workflow_url: ${{ needs.get_workflow_url.outputs.url }}
      should_notify: ${{startsWith(github.event.pull_request.head.ref, 'hotfix-release/')}}
    secrets: inherit
